import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { useUserStore } from './user'
import { BASE_URL } from '../config'

export const useWebSocketStore = defineStore('websocket', () => {
  // 状态
  const socket = ref(null)
  const isConnected = ref(false)
  const connectionStatus = ref('disconnected') // disconnected, connecting, connected, error
  const lastMessage = ref(null)
  const heartbeatTimer = ref(null)
  const reconnectTimer = ref(null)
  const reconnectAttempts = ref(0)
  const maxReconnectAttempts = ref(5)

  // 面试状态
  const interviewStatus = ref('idle') // idle, listening, thinking, speaking
  const currentSessionId = ref('')
  const ephemeralToken = ref('')
  const currentDomain = ref('技术面试')

  // 计算属性
  const canSendMessage = computed(() => isConnected.value && socket.value)

  // 设置临时令牌
  const setEphemeralToken = (token, domain) => {
    ephemeralToken.value = token
    currentDomain.value = domain
  }

  // WebSocket URL - 直连Gemini Live API
  const getWebSocketUrl = (token) => {
    // 根据官方文档，使用正确的WebSocket端点
    const wsUrl = 'wss://generativelanguage.googleapis.com/ws/google.ai.generativelanguage.v1beta.GenerativeService.BidiGenerateContent'
    return `${wsUrl}?access_token=${token}`
  }
  
  // 连接WebSocket - 直连Gemini Live API
  const connect = () => {
    return new Promise(async (resolve, reject) => {
      if (socket.value && isConnected.value) {
        console.log('WebSocket已连接')
        resolve()
        return
      }

      if (!ephemeralToken.value) {
        console.error('无法连接WebSocket：临时令牌不存在')
        uni.showToast({
          title: '连接失败，请重启应用',
          icon: 'none'
        })
        reject(new Error('临时令牌不存在'))
        return
      }

      connectionStatus.value = 'connecting'

      try {
        socket.value = uni.connectSocket({
          url: getWebSocketUrl(ephemeralToken.value),
        success: () => {
          console.log('WebSocket连接请求发送成功')
        },
        fail: (error) => {
          console.error('WebSocket连接失败:', error)
          connectionStatus.value = 'error'
          scheduleReconnect()
        }
      })
      
      // 监听连接打开
      socket.value.onOpen(() => {
        console.log('Gemini Live API WebSocket连接已打开')
        isConnected.value = true
        connectionStatus.value = 'connected'
        reconnectAttempts.value = 0

        // 发送初始配置消息
        sendSetupMessage()
        resolve()
      })

      // 监听消息
      socket.value.onMessage((event) => {
        handleGeminiMessage(event.data)
      })

      // 监听连接关闭
      socket.value.onClose(() => {
        console.log('Gemini Live API WebSocket连接已关闭')
        isConnected.value = false
        connectionStatus.value = 'disconnected'
        scheduleReconnect()
      })

      // 监听错误
      socket.value.onError((error) => {
        console.error('Gemini Live API WebSocket错误:', error)
        connectionStatus.value = 'error'
        scheduleReconnect()
        reject(error)
      })
      
    } catch (error) {
      console.error('创建WebSocket连接失败:', error)
      connectionStatus.value = 'error'
      scheduleReconnect()
      reject(error)
    }
  })
}
  
  // 断开连接
  const disconnect = () => {
    if (socket.value) {
      socket.value.close()
      socket.value = null
    }
    isConnected.value = false
    connectionStatus.value = 'disconnected'
    interviewStatus.value = 'idle'
    currentSessionId.value = ''
    ephemeralToken.value = '' // 清除令牌
    stopReconnect()
  }
  
  // 发送初始配置消息
  const sendSetupMessage = () => {
    const setupMessage = {
      setup: {
        model: "models/gemini-2.0-flash-live-001",
        generation_config: {
          response_modalities: ["AUDIO"],
          speech_config: {
            voice_config: {
              prebuilt_voice_config: {
                voice_name: "Aoede"
              }
            }
          }
        }
      }
    }

    sendMessage(setupMessage)
  }

  // 发送消息到Gemini Live API
  const sendMessage = (message) => {
    if (!canSendMessage.value) {
      console.error('WebSocket未连接，无法发送消息')
      return false
    }

    try {
      const messageStr = typeof message === 'string' ? message : JSON.stringify(message)
      socket.value.send({
        data: messageStr,
        success: () => {
          console.log('消息发送成功:', messageStr)
        },
        fail: (error) => {
          console.error('消息发送失败:', error)
        }
      })
      return true
    } catch (error) {
      console.error('发送消息异常:', error)
      return false
    }
  }
  
  // 处理来自Gemini Live API的消息
  const handleGeminiMessage = (data) => {
    try {
      const message = JSON.parse(data)
      lastMessage.value = message

      console.log('收到Gemini消息:', message)

      // 处理Gemini Live API的响应格式
      if (message.serverContent) {
        if (message.serverContent.modelTurn) {
          const modelTurn = message.serverContent.modelTurn

          // 处理音频输出
          if (modelTurn.parts) {
            for (const part of modelTurn.parts) {
              if (part.inlineData && part.inlineData.mimeType === 'audio/pcm') {
                // 播放音频
                playAudio(part.inlineData.data)
                interviewStatus.value = 'speaking'
              }
            }
          }
        }

        // 检查是否完成
        if (message.serverContent.turnComplete) {
          interviewStatus.value = 'idle'
        }
      }

      // 处理设置确认
      if (message.setupComplete) {
        console.log('Gemini Live API设置完成')
        interviewStatus.value = 'idle'
      }

    } catch (error) {
      console.error('解析Gemini消息失败:', error)
    }
  }
  
  // 播放音频
  const playAudio = (audioData) => {
    // TODO: 实现音频播放逻辑
    console.log('播放音频:', audioData)
  }
  
  // 发送音频数据到Gemini Live API
  const sendAudio = (audioData) => {
    console.log('sendAudio被调用，准备发送音频数据到Gemini Live API:', audioData)
    interviewStatus.value = 'thinking'

    const message = {
      clientContent: {
        turns: [{
          role: "user",
          parts: [{
            inlineData: {
              mimeType: "audio/pcm",
              data: audioData
            }
          }]
        }],
        turnComplete: true
      }
    }
    console.log('构造的Gemini消息:', message)
    const result = sendMessage(message)
    console.log('sendMessage返回结果:', result)
    return result
  }

  // 发送打断信号
  const sendInterrupt = () => {
    // Gemini Live API的打断机制
    const message = {
      clientContent: {
        turns: [],
        turnComplete: true
      }
    }
    interviewStatus.value = 'idle'
    return sendMessage(message)
  }
  
  // Gemini Live API不需要心跳机制，删除相关代码
  
  // 重连机制
  const scheduleReconnect = () => {
    if (reconnectAttempts.value >= maxReconnectAttempts.value) {
      console.log('达到最大重连次数，停止重连')
      return
    }
    
    stopReconnect()
    
    const delay = Math.min(1000 * Math.pow(2, reconnectAttempts.value), 30000) // 指数退避，最大30秒
    console.log(`${delay}ms后尝试重连...`)
    
    reconnectTimer.value = setTimeout(() => {
      reconnectAttempts.value++
      connect()
    }, delay)
  }
  
  const stopReconnect = () => {
    if (reconnectTimer.value) {
      clearTimeout(reconnectTimer.value)
      reconnectTimer.value = null
    }
  }
  
  return {
    // 状态
    socket,
    isConnected,
    connectionStatus,
    lastMessage,
    interviewStatus,
    currentSessionId,
    ephemeralToken,
    currentDomain,

    // 计算属性
    canSendMessage,

    // 动作
    connect,
    disconnect,
    setEphemeralToken,
    sendMessage,
    sendAudio,
    sendInterrupt,
    handleGeminiMessage
  }
})
