<template>
  <view id="app">
    <!-- 应用根组件 -->
  </view>
</template>

<script setup>
import { onLaunch, onShow, onHide } from '@dcloudio/uni-app'
import { useUserStore } from '@/stores/user'

// 应用启动
onLaunch(() => {
  console.log('App Launch')
  initApp()
})

// 应用显示
onShow(() => {
  console.log('App Show')
})

// 应用隐藏
onHide(() => {
  console.log('App Hide')
})

// 初始化应用
const initApp = async () => {
  // 从本地存储恢复用户状态，这将自动触发临时令牌的获取（如果已登录）
  const userStore = useUserStore()
  await userStore.initFromStorage()
}
</script>

<style lang="scss">
/* 全局样式 */
page {
  background-color: #f8f8f8;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
}

/* 通用样式 */
.container {
  padding: 20rpx;
}

.btn-primary {
  background-color: #007aff;
  color: white;
  border-radius: 10rpx;
  padding: 20rpx 40rpx;
  border: none;
  font-size: 32rpx;
}

.btn-primary:active {
  background-color: #0056cc;
}

.text-center {
  text-align: center;
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex-column {
  flex-direction: column;
}
</style>
