package service

import (
	"context"
	"errors"
	"fmt"
	"time"

	"gorm.io/gorm"

	// 导入 config 包
	"interviewmaster/internal/config"
	"interviewmaster/internal/model"
	"interviewmaster/internal/utils"
	"interviewmaster/pkg/database"
	"interviewmaster/pkg/redis"
	"interviewmaster/pkg/sms" // 导入 sms 包
)

// UserService 用户服务
type UserService struct {
	db *gorm.DB
}

// NewUserService 创建用户服务实例
func NewUserService() *UserService {
	return &UserService{
		db: database.GetDB(),
	}
}

// GetUserByPhone 根据手机号获取用户
func (s *UserService) GetUserByPhone(phone string) (*model.User, error) {
	var user model.User
	err := s.db.Where("phone = ?", phone).First(&user).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &user, nil
}

// GetUserByID 根据ID获取用户
func (s *UserService) GetUserByID(id uint64) (*model.User, error) {
	var user model.User
	err := s.db.Where("id = ?", id).First(&user).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &user, nil
}

// CreateUser 创建用户
func (s *UserService) CreateUser(phone string) (*model.User, error) {
	// 检查用户是否已存在
	existingUser, err := s.GetUserByPhone(phone)
	if err != nil {
		return nil, err
	}
	if existingUser != nil {
		return nil, errors.New("用户已存在")
	}

	// 创建新用户
	user := &model.User{
		Phone:          phone,
		FreeTrialCount: 3, // 默认3次免费试用
	}

	// 创建用户
	if err := s.db.Create(user).Error; err != nil {
		return nil, err
	}

	// 创建用户后设置A/B测试分组
	abTestGroup := utils.GetABTestGroup(user.ID, []string{"A", "B"})
	user.ABTestGroup = abTestGroup
	if err := s.db.Save(user).Error; err != nil {
		return nil, err
	}

	return user, nil
}

// UpdateUser 更新用户信息
func (s *UserService) UpdateUser(user *model.User) error {
	return s.db.Save(user).Error
}

// UpdateUserBalance 更新用户余额
func (s *UserService) UpdateUserBalance(userID uint64, countDelta int32, durationDelta int32) error {
	return s.db.Transaction(func(tx *gorm.DB) error {
		var user model.User
		if err := tx.Where("id = ?", userID).First(&user).Error; err != nil {
			return err
		}

		// 更新次数余额
		if countDelta != 0 {
			newCount := int32(user.BalanceCount) + countDelta
			if newCount < 0 {
				return errors.New("余额次数不足")
			}
			user.BalanceCount = uint32(newCount)
		}

		// 更新时长余额
		if durationDelta != 0 {
			newDuration := int32(user.BalanceDuration) + durationDelta
			if newDuration < 0 {
				return errors.New("余额时长不足")
			}
			user.BalanceDuration = uint32(newDuration)
		}

		return tx.Save(&user).Error
	})
}

// ConsumeUserBalance 消费用户余额
func (s *UserService) ConsumeUserBalance(userID uint64, consumeType string) error {
	return s.db.Transaction(func(tx *gorm.DB) error {
		var user model.User
		if err := tx.Where("id = ?", userID).First(&user).Error; err != nil {
			return err
		}

		// 优先消费免费试用次数
		if user.FreeTrialCount > 0 {
			user.FreeTrialCount--
			return tx.Save(&user).Error
		}

		// 根据消费类型扣除相应余额
		switch consumeType {
		case "count":
			if user.BalanceCount <= 0 {
				return errors.New("余额次数不足")
			}
			user.BalanceCount--
		case "duration":
			// 这里可以根据实际使用时长扣除
			// 暂时按每次使用扣除固定时长
			consumeDuration := uint32(300) // 5分钟
			if user.BalanceDuration < consumeDuration {
				return errors.New("余额时长不足")
			}
			user.BalanceDuration -= consumeDuration
		default:
			return errors.New("无效的消费类型")
		}

		return tx.Save(&user).Error
	})
}

// SMSCodeService 短信验证码服务
type SMSCodeService struct {
	db        *gorm.DB
	smsClient *sms.AliyunSMSClient // 添加短信客户端
}

// NewSMSCodeService 创建短信验证码服务实例
func NewSMSCodeService(smsConfig config.AliyunSMSConfig) (*SMSCodeService, error) {
	// 将 config.AliyunSMSConfig 转换为 sms.AliyunSMSConfig
	smsClientConfig := sms.AliyunSMSConfig{
		AccessKeyID:     smsConfig.AccessKeyID,
		AccessKeySecret: smsConfig.AccessKeySecret,
		SignName:        smsConfig.SignName,
		TemplateCode:    smsConfig.TemplateCode,
		Region:          smsConfig.Region,
		Test:            smsConfig.Test,
		TestCode:        smsConfig.TestCode,
	}
	smsClient, err := sms.NewAliyunSMSClient(smsClientConfig)
	if err != nil {
		return nil, fmt.Errorf("创建阿里云短信客户端失败: %v", err)
	}
	return &SMSCodeService{
		db:        database.GetDB(),
		smsClient: smsClient,
	}, nil
}

// SendSMSCode 发送短信验证码
func (s *SMSCodeService) SendSMSCode(phone string, codeType uint8) (string, error) {
	// 检查发送频率限制
	ctx := context.Background()
	key := fmt.Sprintf("sms_limit:%s", phone)

	// 检查是否在60秒内已发送过
	exists, err := redis.Exists(ctx, key)
	if err != nil {
		return "", err
	}
	if exists > 0 {
		return "", errors.New("发送过于频繁，请稍后再试")
	}

	// 生成验证码
	code := utils.GenerateSMSCode()

	// 保存到数据库
	smsCode := &model.SMSCode{
		Phone:     phone,
		Code:      code,
		Type:      codeType,
		Used:      0,
		ExpiresAt: time.Now().Add(5 * time.Minute), // 5分钟过期
	}

	if err := s.db.Create(smsCode).Error; err != nil {
		return "", err
	}

	// 设置发送频率限制（60秒）
	redis.Set(ctx, key, "1", 60*time.Second)

	// 调用阿里云短信服务发送验证码
	if s.smsClient.TestMode() {
		// 如果是测试模式，直接返回测试验证码
		return s.smsClient.GetTestCode(), nil
	}

	err = s.smsClient.SendVerificationCode(phone, code)
	if err != nil {
		return "", fmt.Errorf("发送短信失败: %v", err)
	}

	return code, nil
}

// VerifySMSCode 验证短信验证码
func (s *SMSCodeService) VerifySMSCode(phone, code string, codeType uint8) error {
	// 如果是测试模式，直接验证测试验证码
	if s.smsClient.TestMode() {
		if code == s.smsClient.GetTestCode() {
			return nil // 测试验证码正确
		} else {
			return errors.New("验证码无效或已过期")
		}
	}

	// 正常模式：查询数据库验证
	var smsCode model.SMSCode
	err := s.db.Where("phone = ? AND code = ? AND type = ? AND used = 0 AND expires_at > ?",
		phone, code, codeType, time.Now()).First(&smsCode).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("验证码无效或已过期")
		}
		return err
	}

	// 标记验证码为已使用
	smsCode.Used = 1
	return s.db.Save(&smsCode).Error
}

// CleanExpiredSMSCodes 清理过期的短信验证码
func (s *SMSCodeService) CleanExpiredSMSCodes() error {
	return s.db.Where("expires_at < ?", time.Now()).Delete(&model.SMSCode{}).Error
}
