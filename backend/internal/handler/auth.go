package handler

import (
	"log" // 导入 log 包用于错误处理
	"net/http"

	"github.com/gin-gonic/gin"

	"interviewmaster/internal/config" // 导入 config 包
	"interviewmaster/internal/model"
	"interviewmaster/internal/service"
	"interviewmaster/internal/utils"
	"interviewmaster/pkg/jwt"
)

// AuthHandler 认证处理器
type AuthHandler struct {
	userService    *service.UserService
	smsCodeService *service.SMSCodeService
}

// NewAuthHandler 创建认证处理器实例
func NewAuthHandler() *AuthHandler {
	// 确保配置已加载
	if config.GlobalConfig == nil {
		log.Fatalf("配置未加载，无法初始化 AuthHandler")
	}

	smsCodeService, err := service.NewSMSCodeService(config.GlobalConfig.AliyunSMS)
	if err != nil {
		log.Fatalf("初始化短信验证码服务失败: %v", err)
	}

	return &AuthHandler{
		userService:    service.NewUserService(),
		smsCodeService: smsCodeService,
	}
}

// SendSMSRequest 发送短信验证码请求
type SendSMSRequest struct {
	Phone string `json:"phone" binding:"required"`
	Type  uint8  `json:"type" binding:"required,min=1,max=3"`
}

// SendSMS 发送短信验证码
func (h *AuthHandler) SendSMS(c *gin.Context) {
	var req SendSMSRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ValidationErrorResponse(c, "参数错误: "+err.Error())
		return
	}

	// 验证手机号格式
	if !utils.IsValidPhone(req.Phone) {
		utils.ValidationErrorResponse(c, "手机号格式不正确")
		return
	}

	// 发送短信验证码
	code, err := h.smsCodeService.SendSMSCode(req.Phone, req.Type)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, err.Error())
		return
	}

	// 在开发环境下返回验证码（生产环境不应该返回）
	response := gin.H{"message": "验证码发送成功"}
	if gin.Mode() == gin.DebugMode {
		response["code"] = code // 仅在调试模式下返回
	}

	utils.SuccessResponse(c, response)
}

// LoginRequest 登录请求
type LoginRequest struct {
	Phone string `json:"phone" binding:"required"`
	Code  string `json:"code" binding:"required"`
}

// Login 用户登录
func (h *AuthHandler) Login(c *gin.Context) {
	var req LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ValidationErrorResponse(c, "参数错误: "+err.Error())
		return
	}

	// 验证手机号格式
	if !utils.IsValidPhone(req.Phone) {
		utils.ValidationErrorResponse(c, "手机号格式不正确")
		return
	}

	// 验证短信验证码格式
	if !utils.IsValidSMSCode(req.Code) {
		utils.ValidationErrorResponse(c, "验证码格式不正确")
		return
	}

	// 验证短信验证码
	if err := h.smsCodeService.VerifySMSCode(req.Phone, req.Code, model.SMSTypeLogin); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, err.Error())
		return
	}

	// 获取用户信息
	user, err := h.userService.GetUserByPhone(req.Phone)
	if err != nil {
		utils.InternalErrorResponse(c, "服务器内部错误")
		return
	}

	// 如果用户不存在，则创建新用户
	if user == nil {
		user, err = h.userService.CreateUser(req.Phone)
		if err != nil {
			utils.InternalErrorResponse(c, "创建用户失败")
			return
		}
	}

	// 生成JWT token
	token, err := jwt.GenerateToken(user.ID, user.Phone)
	if err != nil {
		utils.InternalErrorResponse(c, "生成token失败")
		return
	}

	utils.SuccessResponse(c, gin.H{
		"token": token,
		"user":  user,
	})
}

// RefreshTokenRequest 刷新token请求
type RefreshTokenRequest struct {
	Token string `json:"token" binding:"required"`
}

// RefreshToken 刷新token
func (h *AuthHandler) RefreshToken(c *gin.Context) {
	var req RefreshTokenRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ValidationErrorResponse(c, "参数错误: "+err.Error())
		return
	}

	// 刷新token
	newToken, err := jwt.RefreshToken(req.Token)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, err.Error())
		return
	}

	utils.SuccessResponse(c, gin.H{
		"token": newToken,
	})
}

// Logout 用户登出
func (h *AuthHandler) Logout(c *gin.Context) {
	// 在实际应用中，可以将token加入黑名单
	// 这里简单返回成功
	utils.SuccessResponseWithMessage(c, "登出成功", nil)
}

// VerifyToken 验证token
func (h *AuthHandler) VerifyToken(c *gin.Context) {
	token := c.GetHeader("Authorization")
	if token == "" {
		utils.UnauthorizedResponse(c, "缺少认证头")
		return
	}

	// 移除Bearer前缀
	if len(token) > 7 && token[:7] == "Bearer " {
		token = token[7:]
	}

	// 验证token
	if !jwt.ValidateToken(token) {
		utils.UnauthorizedResponse(c, "无效的token")
		return
	}

	utils.SuccessResponseWithMessage(c, "token有效", nil)
}
