{"server": {"host": "0.0.0.0", "port": "8090", "mode": "debug", "read_timeout": 60, "write_timeout": 60}, "database": {"mysql": {"host": "localhost", "port": "3306", "username": "root", "password": "kangli123", "database": "interview_master", "charset": "utf8mb4", "parse_time": true, "loc": "Local", "max_idle_conns": 10, "max_open_conns": 100, "conn_max_lifetime": 3600}, "redis": {"host": "localhost", "port": "6379", "password": "", "db": 0, "pool_size": 10, "min_idle_conns": 5}}, "aliyun_sms": {"access_key_id": "LTAI5tLE4oyEJueFYp74L9mu", "access_key_secret": "******************************", "sign_name": "深圳市博威兴电子科技", "template_code": "SMS_464475690", "test": true, "testCode": "123455", "region": "cn-shenzhen"}, "jwt": {"secret": "wkdkkj7d4zld0qm1K@hdks2pdd7cjUJnf", "expire_hours": "24h"}, "gemini": {"api_key": "AIzaSyDUypHKLOJiJolVX41UTM8r8X8p7Rw-cKI", "model": "models/gemini-live-2.5-flash-preview", "base_url": "https://generativelanguage.googleapis.com", "timeout": 30, "max_concurrent_sessions": 100}, "sms": {"aliyun": {"access_key_id": "your-aliyun-access-key-id", "access_key_secret": "your-aliyun-access-key-secret", "sign_name": "面试助手", "template_code": "SMS_464475690", "region": "cn-shenzhen", "test": true, "testCode": "123456"}}, "payment": {"wechat": {"app_id": "your_wechat_app_id", "mch_id": "your_merchant_id", "api_key": "your_api_key", "notify_url": "https://your-domain.com/api/payment/wechat/notify"}, "alipay": {"app_id": "your_alipay_app_id", "private_key": "your_private_key", "public_key": "your_public_key", "notify_url": "https://your-domain.com/api/payment/alipay/notify"}}, "ab_test": {"enabled": true, "default_group": "A", "groups": ["A", "B"]}, "log": {"level": "info", "file_path": "logs/app.log", "max_size": 100, "max_backups": 5, "max_age": 30}}